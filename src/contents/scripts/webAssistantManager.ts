import React from 'react';
import { createRoot } from 'react-dom/client';
import type { Root } from 'react-dom/client';
import SelectionBarComponent from '../components/SelectionBar/index';
import FloatingButtonComponent from '../components/FloatingButton/index';
import AIProcessModalComponent from '../components/AIProcessModal/index';

// 导入样式文本用于注入到 Shadow DOM
import selectionBarStyleText from "data-text:../components/SelectionBar/index.module.less";
import floatingButtonStyleText from "data-text:../components/FloatingButton/index.module.less";
import aiProcessModalStyleText from "data-text:../components/AIProcessModal/index.module.less";

/**
 * Web Assistant 主管理器
 * 统一管理划词工具栏和浮动按钮
 */
class WebAssistantManager {
  private mainContainer: HTMLDivElement | null = null;
  private shadowRoot: ShadowRoot | null = null;
  private selectionBarContainer: HTMLDivElement | null = null;
  private floatingButtonContainer: HTMLDivElement | null = null;
  private aiProcessModalContainer: HTMLDivElement | null = null;
  private selectionBarRoot: Root | null = null;
  private floatingButtonRoot: Root | null = null;
  private aiProcessModalRoot: Root | null = null;

  private selectedText: string = '';
  private isSelectionBarVisible: boolean = false;
  private isFloatingButtonVisible: boolean = true;
  private isAIProcessModalVisible: boolean = false;

  // AIProcessModal相关状态
  private currentAIAction: string = '';
  private aiModalPosition: { x: number; y: number } = { x: 0, y: 0 };
  private cachedSelectionRect: DOMRect | null = null; // 缓存选择区域的位置信息

  constructor() {
    console.log('WebAssistantManager: Constructor called');
    this.init();
  }

  private init(): void {
    console.log('WebAssistantManager: Initializing...');
    try {
      this.createMainContainer();
      this.initEventListeners();
      this.initFloatingButton();
      console.log('WebAssistantManager: Initialized successfully');
    } catch (error) {
      console.error('WebAssistantManager: Initialization failed:', error);
    }
  }

  /**
   * 创建主容器和 Shadow DOM
   */
  private createMainContainer(): void {
    console.log('WebAssistantManager: Creating main container...');

    // 检查是否已经存在主容器，避免重复创建
    const existingContainer = document.getElementById('web-assistant-main-container');
    if (existingContainer) {
      console.log('WebAssistantManager: Main container already exists, skipping creation');
      return;
    }

    // 创建主容器
    this.mainContainer = document.createElement('div');
    this.mainContainer.id = 'web-assistant-main-container';
    this.mainContainer.className = 'web-assistant-container';

    // 创建 Shadow Root
    this.shadowRoot = this.mainContainer.attachShadow({ mode: 'open' });
    console.log('WebAssistantManager: Shadow root created');

    // 添加样式
    const style = document.createElement('style');
    style.textContent = this.getStyles() + '\n' + selectionBarStyleText + '\n' + floatingButtonStyleText + '\n' + aiProcessModalStyleText;
    this.shadowRoot.appendChild(style);

    // 创建划词工具栏容器
    this.selectionBarContainer = document.createElement('div');
    this.selectionBarContainer.id = 'web-assistant-selection-bar';
    this.selectionBarContainer.className = 'web-assistant-selection-bar-container';
    this.shadowRoot.appendChild(this.selectionBarContainer);

    // 创建浮动按钮容器
    this.floatingButtonContainer = document.createElement('div');
    this.floatingButtonContainer.id = 'web-assistant-floating-button';
    this.floatingButtonContainer.className = 'web-assistant-floating-button-container';
    this.shadowRoot.appendChild(this.floatingButtonContainer);

    // 创建AIProcessModal容器
    this.aiProcessModalContainer = document.createElement('div');
    this.aiProcessModalContainer.id = 'web-assistant-ai-process-modal';
    this.aiProcessModalContainer.className = 'web-assistant-ai-process-modal-container';
    this.shadowRoot.appendChild(this.aiProcessModalContainer);

    // 添加到页面
    document.body.appendChild(this.mainContainer);
    console.log('WebAssistantManager: Main container added to body');
  }

  /**
   * 初始化事件监听器
   */
  private initEventListeners(): void {
    // 监听文本选择
    document.addEventListener('mouseup', this.handleMouseUp);
    document.addEventListener('keyup', this.handleKeyUp);

    // 监听点击事件（用于隐藏工具栏）
    document.addEventListener('click', this.handleDocumentClick);

    // 监听选择变化
    document.addEventListener('selectionchange', this.handleSelectionChange);

    // 监听页面卸载，清理所有弹窗
    window.addEventListener('beforeunload', this.hideAllModals);

    // 监听页面隐藏，清理所有弹窗
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.hideAllModals();
      }
    });
  }

  /**
   * 处理鼠标抬起事件
   */
  private handleMouseUp = (event: MouseEvent): void => {
    console.log('Mouse up event triggered');

    // 延迟处理，确保选择已完成
    setTimeout(() => {
      this.checkSelection(event);
    }, 10);
  };

  /**
   * 处理键盘事件
   */
  private handleKeyUp = (event: KeyboardEvent): void => {
    // 延迟处理，确保选择已完成
    setTimeout(() => {
      this.checkSelection();
    }, 10);
  };

  /**
   * 处理选择变化事件
   */
  private handleSelectionChange = (): void => {
    // 延迟处理，避免在选择过程中过早隐藏工具栏
    setTimeout(() => {
      const selection = window.getSelection();
      if (!selection || selection.toString().trim() === '') {
        console.log('Selection cleared, hiding toolbar');
        this.hideSelectionBar();
      }
    }, 100);
  };

  /**
   * 处理文档点击事件
   */
  private handleDocumentClick = (event: MouseEvent): void => {
    // 如果点击的不是工具栏内部，且工具栏可见，则隐藏工具栏
    if (this.isSelectionBarVisible && this.selectionBarContainer) {
      const target = event.target as Node;

      // 使用 composedPath 获取完整的事件路径，包括Shadow DOM内的元素
      const eventPath = event.composedPath();

      // 检查事件路径中是否包含我们的容器
      const isInsideMainContainer = this.mainContainer && eventPath.includes(this.mainContainer);
      const isInsideShadowDOM = this.shadowRoot && eventPath.some(node =>
        this.shadowRoot!.contains(node as Node)
      );
      const isInsideSelectionBar = this.selectionBarContainer && eventPath.some(node =>
        this.selectionBarContainer!.contains(node as Node)
      );

      console.log('Document click:', {
        target: target,
        eventPathLength: eventPath.length,
        isInsideShadowDOM: isInsideShadowDOM,
        isInsideMainContainer: isInsideMainContainer,
        isInsideSelectionBar: isInsideSelectionBar
      });

      // 如果点击在我们的组件内部，不隐藏工具栏
      if (isInsideShadowDOM || isInsideMainContainer || isInsideSelectionBar) {
        console.log('Click inside selection bar area, not hiding');
        return;
      }

      // 检查是否还有选中的文本
      const selection = window.getSelection();
      if (!selection || selection.toString().trim() === '') {
        console.log('Hiding selection bar due to outside click');
        this.hideSelectionBar();
      }
    }
  };

  /**
   * 检查当前选择
   */
  private checkSelection(event?: MouseEvent): void {
    const selection = window.getSelection();
    if (!selection) return;

    const newSelectedText = selection.toString().trim();
    console.log('WebAssistantManager: Checking selection:', newSelectedText);

    if (newSelectedText && newSelectedText !== this.selectedText) {
      this.selectedText = newSelectedText;
      this.showSelectionBar(event);
    } else if (!newSelectedText && this.isSelectionBarVisible) {
      // 简化逻辑：选择清空时直接隐藏SelectionBar
      // AIProcessModal有自己独立的生命周期管理
      this.hideSelectionBar();
    }
  }

  /**
   * 显示划词工具栏
   */
  private showSelectionBar(event?: MouseEvent): void {
    if (!this.selectedText || !this.selectionBarContainer) return;

    console.log('WebAssistantManager: Showing selection bar for text:', this.selectedText);

    // 预先计算并缓存选择区域的位置信息，避免后续操作时选择状态丢失
    this.cacheSelectionPosition();

    // 创建 React root（如果还没有）
    if (!this.selectionBarRoot) {
      this.selectionBarRoot = createRoot(this.selectionBarContainer);
    }

    // 渲染组件
    this.selectionBarRoot.render(
      React.createElement(SelectionBarComponent, {
        selectedText: this.selectedText,
        onAction: this.handleSelectionBarAction,
        onClose: this.hideSelectionBar
      })
    );

    // 定位工具栏
    this.positionSelectionBar();

    // 显示工具栏
    this.selectionBarContainer.classList.add('show');
    this.isSelectionBarVisible = true;

    console.log('WebAssistantManager: Selection bar shown');
  }

  /**
   * 隐藏划词工具栏
   */
  private hideSelectionBar = (): void => {
    if (this.selectionBarContainer && this.isSelectionBarVisible) {
      // 添加隐藏动画类
      this.selectionBarContainer.classList.remove('show');
      this.selectionBarContainer.classList.add('hide');

      // 等待动画完成后重置状态
      setTimeout(() => {
        if (this.selectionBarContainer) {
          this.selectionBarContainer.classList.remove('hide');
        }
        this.isSelectionBarVisible = false;
        // 保留selectedText和cachedSelectionRect给AIProcessModal使用
        // 注意：不再重置AIProcessModal状态，保持独立性
      }, 200); // 与CSS动画时长匹配

      console.log('WebAssistantManager: Selection bar hiding with animation');
    }
  };

  /**
   * 显示AIProcessModal
   */
  private showAIProcessModal(action: string): void {
    if (!this.selectedText || !this.aiProcessModalContainer) return;

    console.log('WebAssistantManager: Showing AI process modal for action:', action);

    // 设置当前AI操作和状态
    this.currentAIAction = action;
    this.isAIProcessModalVisible = true;

    // 计算弹窗位置
    this.calculateAIModalPosition();

    // 先隐藏SelectionBar（实现互斥显示）
    this.hideSelectionBar();

    // 添加小延迟，让SelectionBar的隐藏动画先播放
    setTimeout(() => {
      // 创建React root（如果还没有）
      if (!this.aiProcessModalRoot) {
        this.aiProcessModalRoot = createRoot(this.aiProcessModalContainer);
      }

      // 渲染AIProcessModal组件
      this.aiProcessModalRoot.render(
        React.createElement(AIProcessModalComponent, {
          isVisible: true,
          selectedText: this.selectedText,
          actionType: this.currentAIAction,
          onClose: this.hideAIProcessModal,
          position: this.aiModalPosition
        })
      );

      console.log('WebAssistantManager: AI process modal shown');
    }, 100); // 100ms延迟，让SelectionBar隐藏动画先播放
  }

  /**
   * 隐藏AIProcessModal
   */
  private hideAIProcessModal = (): void => {
    if (this.aiProcessModalContainer && this.isAIProcessModalVisible) {
      console.log('WebAssistantManager: Hiding AI process modal');

      // 清空容器内容
      if (this.aiProcessModalRoot) {
        this.aiProcessModalRoot.render(null);
      }

      // 重置状态
      this.isAIProcessModalVisible = false;
      this.currentAIAction = '';
      this.selectedText = '';
      this.cachedSelectionRect = null; // 清理缓存的选择位置

      console.log('WebAssistantManager: AI process modal hidden');
    }
  };

  /**
   * 缓存当前选择区域的位置信息
   */
  private cacheSelectionPosition(): void {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      this.cachedSelectionRect = null;
      return;
    }

    const range = selection.getRangeAt(0);
    this.cachedSelectionRect = range.getBoundingClientRect();
    console.log('WebAssistantManager: Cached selection position:', this.cachedSelectionRect);
  }

  /**
   * 计算AIProcessModal的位置
   */
  private calculateAIModalPosition(): void {
    // 优先使用缓存的选择位置，如果没有则尝试获取当前选择
    let rect = this.cachedSelectionRect;

    if (!rect) {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) {
        console.warn('WebAssistantManager: No selection found and no cached position, using default position');
        this.aiModalPosition = { x: 0, y: 0 };
        return;
      }
      const range = selection.getRangeAt(0);
      rect = range.getBoundingClientRect();
    }

    console.log('WebAssistantManager: Calculating modal position using rect:', rect);

    // 使用与SelectionBar相同的定位逻辑
    const offsetY = 8;
    const modalWidth = 500; // AI弹窗的实际宽度
    const modalHeight = 400; // AI弹窗的估算高度

    let left = rect.left;
    let top = rect.bottom + offsetY;

    // 确保弹窗不会超出视窗右边界
    const viewportWidth = window.innerWidth;
    if (left + modalWidth > viewportWidth) {
      left = rect.right - modalWidth;
    }

    // 确保弹窗不会超出视窗下边界
    const viewportHeight = window.innerHeight;
    if (top + modalHeight > viewportHeight) {
      top = rect.top - modalHeight - offsetY;
    }

    // 确保不会超出左边界和上边界
    left = Math.max(5, left);
    top = Math.max(5, top);

    this.aiModalPosition = { x: left, y: top };
    console.log('WebAssistantManager: Calculated modal position:', this.aiModalPosition);
  }

  /**
   * 隐藏所有弹窗（用于清理状态）
   */
  private hideAllModals(): void {
    console.log('WebAssistantManager: Hiding all modals');
    this.hideSelectionBar();
    this.hideAIProcessModal();
  }

  /**
   * 设置AIProcessModal的显示状态（保留用于兼容性）
   */
  public setAIProcessModalVisible(visible: boolean): void {
    this.isAIProcessModalVisible = visible;
    console.log('WebAssistantManager: AIProcessModal visibility set to:', visible);
  }

  /**
   * 定位划词工具栏
   */
  private positionSelectionBar(): void {
    if (!this.selectionBarContainer) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    console.log('WebAssistantManager: Positioning selection bar at:', rect);

    // 计算悬浮球的位置，让它出现在选中文本的下方
    const offsetY = 8; // 垂直偏移，避免紧贴文本
    const barWidth = 300; // 估算的悬浮球宽度
    const barHeight = 50; // 估算的悬浮球高度

    // 在shadow DOM中，主容器使用固定定位，选择栏容器使用绝对定位
    // rect已经是相对于视口的位置，直接使用即可
    let left = rect.left;
    let top = rect.bottom + offsetY;

    // 确保悬浮球不会超出视窗右边界
    const viewportWidth = window.innerWidth;
    if (left + barWidth > viewportWidth) {
      // 如果超出右边界，调整到选中文本的右边界对齐
      left = rect.right - barWidth;
    }

    // 确保悬浮球不会超出视窗下边界
    const viewportHeight = window.innerHeight;
    if (top + barHeight > viewportHeight) {
      // 如果超出下边界，显示在选中文本的上方
      top = rect.top - barHeight - offsetY;
    }

    // 确保不会超出左边界和上边界
    left = Math.max(5, left);
    top = Math.max(5, top);

    this.selectionBarContainer.style.left = `${left}px`;
    this.selectionBarContainer.style.top = `${top}px`;
  }

  /**
   * 初始化浮动按钮
   */
  private initFloatingButton(): void {
    if (!this.floatingButtonContainer) return;
    
    console.log('WebAssistantManager: Initializing floating button...');
    
    // 创建 React root
    this.floatingButtonRoot = createRoot(this.floatingButtonContainer);
    
    // 渲染组件
    this.floatingButtonRoot.render(
      React.createElement(FloatingButtonComponent, {
        onAction: this.handleFloatingButtonAction
      })
    );
    
    console.log('WebAssistantManager: Floating button initialized');
  }

  /**
   * 处理划词工具栏动作
   */
  private handleSelectionBarAction = (action: string): void => {
    console.log('WebAssistantManager: Selection bar action:', action, 'with text:', this.selectedText);

    // 导入isAIAction函数来判断是否为AI操作
    const { isAIAction } = require('../../config/menuItems');

    if (isAIAction(action)) {
      // AI操作：显示AIProcessModal，隐藏SelectionBar
      this.showAIProcessModal(action);
    } else {
      // 非AI操作：保持原有逻辑
      switch (action) {
        case 'open-panel':
          this.handleOpenPanel();
          break;
        default:
          console.log('WebAssistantManager: Unknown action:', action);
      }
    }
  };

  /**
   * 处理浮动按钮动作
   */
  private handleFloatingButtonAction = (action: string): void => {
    console.log('WebAssistantManager: Floating button action:', action);
    
    switch (action) {
      case 'open-panel':
        this.handleOpenPanel();
        break;
      case 'quick-translate':
        this.handleQuickTranslate();
        break;
      case 'quick-summary':
        this.handleQuickSummary();
        break;
      case 'settings':
        this.handleSettings();
        break;
      default:
        console.log('WebAssistantManager: Unknown floating button action:', action);
    }
  };

  // 注意：原有的handleSummary、handleTranslate、handleTextProcessing方法已移除
  // 现在所有AI操作都通过showAIProcessModal统一处理

  /**
   * 处理打开面板
   */
  private handleOpenPanel(): void {
    console.log('WebAssistantManager: Opening AI assistant panel...');

    // 向背景脚本发送消息，请求打开侧边栏面板
    chrome.runtime.sendMessage({
      action: 'open-panel',
      text: this.selectedText
    }).then(() => {
      console.log('WebAssistantManager: Panel open request sent successfully');
      // 关闭划词工具栏
      this.hideSelectionBar();
    }).catch((error) => {
      console.error('WebAssistantManager: Failed to send panel open request:', error);
    });
  }

  /**
   * 处理快速翻译
   */
  private handleQuickTranslate(): void {
    console.log('WebAssistantManager: Quick translate...');
    // TODO: 实现快速翻译功能
  }

  /**
   * 处理快速总结
   */
  private handleQuickSummary(): void {
    console.log('WebAssistantManager: Quick summary...');
    // TODO: 实现快速总结功能
  }

  /**
   * 处理设置
   */
  private handleSettings(): void {
    console.log('WebAssistantManager: Opening settings...');
    // TODO: 实现设置功能
  }

  /**
   * 获取样式
   */
  private getStyles(): string {
    return `
      /* Web Assistant 主样式文件 */

      /* 重置样式 */
      * {
        box-sizing: border-box;
      }

      /* 主容器样式 */
      .web-assistant-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        z-index: 2147483647;
        pointer-events: none;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-size: 14px;
        line-height: 1.4;
      }

      /* 划词工具栏容器 */
      .web-assistant-selection-bar-container {
        position: absolute;
        opacity: 0;
        visibility: hidden;
        pointer-events: auto;
        transform: translateY(-10px) scale(0.95);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .web-assistant-selection-bar-container.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0) scale(1);
      }

      .web-assistant-selection-bar-container.hide {
        opacity: 0;
        visibility: hidden;
        transform: translateY(-5px) scale(0.98);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      }

      /* 浮动按钮容器 */
      .web-assistant-floating-button-container {
        position: fixed;
        pointer-events: auto;
      }

      /* AIProcessModal容器 */
      .web-assistant-ai-process-modal-container {
        position: absolute;
        pointer-events: auto;
        z-index: 10001; /* 比SelectionBar更高的层级 */
      }

      /* 确保所有子元素都有正确的指针事件 */
      #web-assistant-selection-bar,
      #web-assistant-floating-button,
      #web-assistant-ai-process-modal {
        pointer-events: auto;
      }
    `;
  }
}

// 防止重复初始化
declare global {
  interface Window {
    webAssistantManagerInitialized?: boolean;
  }
}

// 初始化 Web Assistant Manager
if (!window.webAssistantManagerInitialized) {
  console.log('WebAssistantManager: Starting initialization...');
  window.webAssistantManagerInitialized = true;
  new WebAssistantManager();
} else {
  console.log('WebAssistantManager: Already initialized, skipping...');
}
