# SecondaryActionButtons 组件

## 概述

SecondaryActionButtons 是一个可复用的二级操作按钮组件，根据菜单项配置动态渲染二级操作按钮。

## 功能特性

- 🎯 **动态渲染**: 根据菜单项ID自动加载对应的二级操作
- 🎨 **美观设计**: 现代化的按钮样式，支持主要操作和普通操作的视觉区分
- 📱 **响应式**: 支持移动端自适应布局
- ♿ **无障碍**: 支持键盘导航和屏幕阅读器
- 🔧 **可配置**: 支持禁用状态和自定义样式

## 使用方法

```tsx
import SecondaryActionButtons from '../SecondaryActionButtons';
import type { SecondaryAction } from '../../../config/menuItems';

// 在组件中使用
<SecondaryActionButtons
  menuItemId="translate"
  onAction={handleSecondaryAction}
  disabled={false}
  className="custom-class"
/>

// 处理二级操作
const handleSecondaryAction = (action: SecondaryAction) => {
  switch (action.id) {
    case 'continue-asking':
      // 处理继续问
      break;
    case 'adjust':
      // 处理调整
      break;
    case 'deprecate':
      // 处理弃用
      break;
    case 'insert-below':
      // 处理插入到下方
      break;
    case 'replace-original':
      // 处理替换原文
      break;
  }
};
```

## Props

| 属性 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| menuItemId | string | ✅ | - | 菜单项ID，用于获取对应的二级操作 |
| onAction | (action: SecondaryAction) => void | ✅ | - | 二级操作回调函数 |
| disabled | boolean | ❌ | false | 是否禁用所有按钮 |
| className | string | ❌ | '' | 自定义CSS类名 |

## 二级操作类型

| 操作ID | 标签 | 图标 | 描述 |
|--------|------|------|------|
| continue-asking | 继续问 | 💭 | 继续对话询问 |
| adjust | 调整 | 🔧 | 调整生成结果 |
| deprecate | 弃用 | ❌ | 弃用当前结果 |
| insert-below | 插入到下方 | 📝 | 在原文下方插入结果 |
| replace-original | 替换原文 | ✅ | 用结果替换原文 |

## 样式特性

- **动画效果**: 按钮支持悬停和点击动画
- **渐变背景**: 主要操作按钮使用渐变背景
- **阴影效果**: 按钮具有立体感的阴影效果
- **响应式布局**: 在小屏幕上自动切换为垂直布局

## 集成说明

该组件已集成到以下位置：
- `AIProcessModal`: 作为AI处理结果的操作按钮
- `SelectionBar`: 显示二级操作数量指示器
- `webAssistantManager`: 样式已注入到Shadow DOM

## 配置文件

二级操作的配置在 `src/config/menuItems.ts` 中定义：
- `secondaryActionTypes`: 定义所有可用的二级操作类型
- `secondaryActionGroups`: 定义不同功能支持的二级操作组合
- 辅助函数: `getSecondaryActions`, `hasSecondaryAction` 等
