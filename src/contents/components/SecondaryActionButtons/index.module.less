.actionButtons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.actionBtn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 14px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: #fff;
  color: #666;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background: #f0f8ff;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.15);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      border-color: #d9d9d9;
      color: #666;
      background: #fff;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      transform: none;
    }
  }
}

.primaryBtn {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: #fff;
  border-color: #1890ff;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);

  &:hover {
    background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
    border-color: #40a9ff;
    color: #fff;
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
  }

  &:disabled {
    background: #d9d9d9;
    border-color: #d9d9d9;
    color: #fff;
    box-shadow: none;

    &:hover {
      background: #d9d9d9;
      border-color: #d9d9d9;
      color: #fff;
      box-shadow: none;
      transform: none;
    }
  }
}

.buttonIcon {
  font-size: 14px;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
}

/* 响应式布局 */
@media (max-width: 480px) {
  .actionButtons {
    flex-direction: column;
    gap: 6px;
  }
  
  .actionBtn {
    width: 100%;
    justify-content: center;
  }
}
