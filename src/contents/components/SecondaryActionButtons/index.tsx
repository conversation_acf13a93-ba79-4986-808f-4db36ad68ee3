import React from 'react';
import { getSecondaryActions } from '../../../config/menuItems';
import type { SecondaryAction } from '../../../config/menuItems';
import * as styles from './index.module.less';

interface SecondaryActionButtonsProps {
  menuItemId: string;
  onAction: (action: SecondaryAction) => void;
  disabled?: boolean;
  className?: string;
}

const SecondaryActionButtons: React.FC<SecondaryActionButtonsProps> = ({
  menuItemId,
  onAction,
  disabled = false,
  className = ''
}) => {
  const secondaryActions = getSecondaryActions(menuItemId);

  if (secondaryActions.length === 0) {
    return null;
  }

  // 获取操作按钮的图标
  const getActionIcon = (action: SecondaryAction): string => {
    switch (action.id) {
      case 'continue-asking':
        return '💭';
      case 'adjust':
        return '🔧';
      case 'deprecate':
        return '❌';
      case 'insert-below':
        return '📝';
      case 'replace-original':
        return '✅';
      default:
        return '•';
    }
  };

  // 判断是否为主要操作（替换原文）
  const isPrimaryAction = (action: SecondaryAction): boolean => {
    return action.id === 'replace-original';
  };

  return (
    <div className={`${styles.actionButtons} ${className}`}>
      {secondaryActions.map((action) => (
        <button
          key={action.id}
          className={`${styles.actionBtn} ${isPrimaryAction(action) ? styles.primaryBtn : ''}`}
          onClick={() => onAction(action)}
          disabled={disabled}
          title={action.label}
        >
          <span className={styles.buttonIcon}>
            {getActionIcon(action)}
          </span>
          {action.label}
        </button>
      ))}
    </div>
  );
};

export default SecondaryActionButtons;
