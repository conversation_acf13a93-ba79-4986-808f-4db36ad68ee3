.selectionBar {
  position: absolute;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 8px 12px;
  z-index: 9999;
  font-family: sans-serif;
  font-size: 16px;
  border: 1px solid #e5e7eb;
  width: fit-content;
  /* 移除组件级别的动画，让容器级别的动画生效 */
}

.selectionIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin: 0 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  &:hover {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.defaultIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #4096FF;
  color: white;
  font-size: 10px;
  font-weight: bold;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.selectionButton {
  display: flex;
  width: 40px;
  align-items: center;
  margin: 0 6px;
  cursor: pointer;
  color: #1c1c1e;
  transition: all 0.2s ease;
  position: relative;

  img {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }

  span {
    margin-left: 4px;
    font-size: 16px;
  }

  &:hover {
    opacity: 0.7;
  }

  &.hasSecondaryActions {
    &:hover {
      opacity: 0.8;
      transform: scale(1.02);
    }
  }
}

.secondaryIndicator {
  position: absolute !important;
  top: -4px;
  right: -4px;
  background: #1890ff;
  color: white;
  font-size: 10px !important;
  font-weight: bold;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  margin: 0 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.selectionMore {
  position: relative;
  display: flex;
  align-items: center;
  margin: 0 6px;
  cursor: pointer;
}

.selectionDots {
  font-size: 16px;
  color: #1c1c1e;

  &:hover {
    opacity: 0.7;
  }
}

.selectionDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  min-width: 120px;
  z-index: 10000;
  border: 1px solid #e5e7eb;
  margin-top: 8px;

  /* 添加下拉动画 */
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px) scale(0.95);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /* 当显示时的状态 */
  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
  }

  /* 添加箭头指示器 */
  &::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid white;
    filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
  }
}

.selectionDropdownItem {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
    color: #007bff;
  }

  &:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  &:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

.selectionClose {
  display: flex;
  align-items: center;
  margin: 0 6px;
  cursor: pointer;
  font-size: 20px;
  font-weight: bold;
  color: #888;
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.7;
  }
}

.selectionDivider {
  width: 1px;
  height: 20px;
  background-color: #ccc;
  margin: 0 6px;
}

.selectionContainer {
  position: relative;
}

.selectionArrow {
  position: absolute;
  top: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
}
